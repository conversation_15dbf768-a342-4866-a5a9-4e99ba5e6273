<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-3"><?= esc($title) ?></h1>
            
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('gcs-test') ?>">GCS Test</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Uploaded Files</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-filter"></i> Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?= current_url() ?>">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="category" class="form-label">Category</label>
                                <select name="category" id="category" class="form-select">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?= esc($cat) ?>" <?= $current_category === $cat ? 'selected' : '' ?>>
                                            <?= ucwords(str_replace('_', ' ', $cat)) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="storage_type" class="form-label">Storage Type</label>
                                <select name="storage_type" id="storage_type" class="form-select">
                                    <option value="">All Storage Types</option>
                                    <option value="local" <?= $current_storage_type === 'local' ? 'selected' : '' ?>>Local</option>
                                    <option value="gcs" <?= $current_storage_type === 'gcs' ? 'selected' : '' ?>>Google Cloud Storage</option>
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="<?= current_url() ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Files List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0"><i class="fas fa-files-o"></i> Uploaded Files</h5>
                    <div>
                        <span class="badge bg-primary"><?= count($files) ?> files</span>
                        <a href="<?= base_url('gcs-test') ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Test
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($files)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No files found</h5>
                            <p class="text-muted">Upload some files to see them here.</p>
                            <a href="<?= base_url('gcs-test') ?>" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Upload Files
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>File Name</th>
                                        <th>Category</th>
                                        <th>Storage</th>
                                        <th>Size</th>
                                        <th>Uploaded</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($files as $file): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-<?= getFileIcon($file['stored_filename']) ?> me-2 text-primary"></i>
                                                    <div>
                                                        <div class="fw-bold"><?= esc($file['original_filename']) ?></div>
                                                        <small class="text-muted"><?= esc($file['stored_filename']) ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= ucwords(str_replace('_', ' ', $file['category'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($file['storage_type'] === 'gcs'): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-cloud"></i> GCS
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-info">
                                                        <i class="fas fa-hdd"></i> Local
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= formatBytes($file['file_size']) ?></td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M j, Y g:i A', strtotime($file['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= base_url('gcs-test/download/' . $file['id']) ?>" 
                                                       class="btn btn-outline-primary" title="Download">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    <?php if ($file['storage_type'] === 'gcs'): ?>
                                                        <a href="<?= base_url('gcs-test/view/' . $file['id']) ?>" 
                                                           class="btn btn-outline-info" title="View" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteFile(<?= $file['id'] ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteFile(fileId) {
    if (confirm('Are you sure you want to delete this file?')) {
        fetch(`<?= base_url('gcs-test/delete/') ?>${fileId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting file: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting file: ' + error.message);
        });
    }
}
</script>

<?php
// Helper functions
function getFileIcon($filename) {
    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    switch ($ext) {
        case 'pdf': return 'pdf';
        case 'doc':
        case 'docx': return 'word';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif': return 'image';
        default: return 'alt';
    }
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>
<?= $this->endSection() ?>
