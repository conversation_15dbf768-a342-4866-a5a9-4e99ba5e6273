<?php

namespace App\Models;

use CodeIgniter\Model;

class ApplicantFilesModel extends Model
{
    protected $table = 'applicant_files';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'applicant_id',
        'file_title',
        'file_description',
        'file_path',
        'file_extracted_texts',
        'gcs_path',
        'storage_type',
        'public_url',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    /**
     * Get the public URL for a file (handles both local and GCS storage)
     *
     * @param array $file File record from database
     * @return string Public URL for the file
     */
    public function getFileUrl(array $file): string
    {
        // If file has a public_url (GCS), use it
        if (!empty($file['public_url'])) {
            return $file['public_url'];
        }

        // If file has gcs_path, generate GCS URL
        if (!empty($file['gcs_path']) && isset($file['storage_type']) && $file['storage_type'] === 'gcs') {
            try {
                $gcsService = new \App\Services\GoogleCloudStorageService();
                return $gcsService->generatePublicUrl($file['gcs_path']);
            } catch (\Exception $e) {
                log_message('error', 'Failed to generate GCS URL: ' . $e->getMessage());
            }
        }

        // Fallback to local file path
        if (!empty($file['file_path'])) {
            return base_url($file['file_path']);
        }

        return '#';
    }

    /**
     * Get files for an applicant with enhanced data
     *
     * @param int $applicantId
     * @return array
     */
    public function getFilesWithUrls(int $applicantId): array
    {
        $files = $this->where('applicant_id', $applicantId)->findAll();

        foreach ($files as &$file) {
            $file['view_url'] = $this->getFileUrl($file);
            $file['storage_display'] = (isset($file['storage_type']) && $file['storage_type'] === 'gcs') ? 'Cloud Storage' : 'Local Storage';
        }

        return $files;
    }
}