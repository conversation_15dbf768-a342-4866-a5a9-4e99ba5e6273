<?php

namespace App\Services;

use Google\Cloud\Storage\StorageClient;
use Google\Cloud\Storage\Bucket;
use CodeIgniter\Files\File;
use Exception;

/**
 * Google Cloud Storage Service
 * 
 * Handles file uploads, downloads, and management with Google Cloud Storage
 */
class GoogleCloudStorageService
{
    private $storage;
    private $bucket;
    private $bucketName;
    private $projectId;

    public function __construct()
    {
        $this->projectId = env('GCS_PROJECT_ID');
        $this->bucketName = env('GCS_BUCKET_NAME');
        
        // Initialize Google Cloud Storage client
        $this->initializeStorage();
    }

    /**
     * Initialize Google Cloud Storage client
     */
    private function initializeStorage()
    {
        try {
            $keyFilePath = env('GCS_KEY_FILE_PATH');

            // Convert relative path to absolute path
            if ($keyFilePath && !str_starts_with($keyFilePath, '/') && !str_contains($keyFilePath, ':\\')) {
                $keyFilePath = ROOTPATH . $keyFilePath;
            }

            if (!$keyFilePath || !file_exists($keyFilePath)) {
                throw new Exception('Google Cloud Storage key file not found: ' . $keyFilePath);
            }

            $this->storage = new StorageClient([
                'projectId' => $this->projectId,
                'keyFilePath' => $keyFilePath
            ]);

            $this->bucket = $this->storage->bucket($this->bucketName);

        } catch (Exception $e) {
            log_message('error', 'GCS Initialization Error: ' . $e->getMessage());
            throw new Exception('Failed to initialize Google Cloud Storage: ' . $e->getMessage());
        }
    }

    /**
     * Upload file to Google Cloud Storage
     * 
     * @param File $file The file to upload
     * @param string $category File category (applicants, applications, etc.)
     * @param string $subPath Optional sub-path within category
     * @return array Upload result with GCS path and public URL
     */
    public function uploadFile(File $file, string $category, string $subPath = ''): array
    {
        try {
            // Generate unique filename
            $originalName = $file->getClientName();
            $extension = $file->getClientExtension();
            $filename = $this->generateUniqueFilename($originalName, $extension);
            
            // Build GCS object path
            $gcsPath = $this->buildGcsPath($category, $subPath, $filename);
            
            // Upload file to GCS
            $object = $this->bucket->upload(
                fopen($file->getTempName(), 'r'),
                [
                    'name' => $gcsPath,
                    'metadata' => [
                        'original_name' => $originalName,
                        'category' => $category,
                        'uploaded_at' => date('Y-m-d H:i:s'),
                        'file_size' => $file->getSize(),
                        'mime_type' => $file->getMimeType()
                    ]
                ]
            );

            // Generate public URL
            $publicUrl = $this->generatePublicUrl($gcsPath);

            return [
                'success' => true,
                'gcs_path' => $gcsPath,
                'public_url' => $publicUrl,
                'filename' => $filename,
                'original_name' => $originalName,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];

        } catch (Exception $e) {
            log_message('error', 'GCS Upload Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Upload file from file path
     * 
     * @param string $filePath Local file path
     * @param string $category File category
     * @param string $subPath Optional sub-path
     * @param string $originalName Original filename
     * @return array Upload result
     */
    public function uploadFromPath(string $filePath, string $category, string $subPath = '', string $originalName = ''): array
    {
        try {
            if (!file_exists($filePath)) {
                throw new Exception('File not found: ' . $filePath);
            }

            $originalName = $originalName ?: basename($filePath);
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
            $filename = $this->generateUniqueFilename($originalName, $extension);
            
            $gcsPath = $this->buildGcsPath($category, $subPath, $filename);
            
            $object = $this->bucket->upload(
                fopen($filePath, 'r'),
                [
                    'name' => $gcsPath,
                    'metadata' => [
                        'original_name' => $originalName,
                        'category' => $category,
                        'uploaded_at' => date('Y-m-d H:i:s'),
                        'file_size' => filesize($filePath),
                        'mime_type' => mime_content_type($filePath)
                    ]
                ]
            );

            $publicUrl = $this->generatePublicUrl($gcsPath);

            return [
                'success' => true,
                'gcs_path' => $gcsPath,
                'public_url' => $publicUrl,
                'filename' => $filename,
                'original_name' => $originalName,
                'file_size' => filesize($filePath),
                'mime_type' => mime_content_type($filePath)
            ];

        } catch (Exception $e) {
            log_message('error', 'GCS Upload from Path Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Download file from GCS
     * 
     * @param string $gcsPath GCS object path
     * @return string|false File content or false on failure
     */
    public function downloadFile(string $gcsPath)
    {
        try {
            $object = $this->bucket->object($gcsPath);
            
            if (!$object->exists()) {
                throw new Exception('File not found in GCS: ' . $gcsPath);
            }

            return $object->downloadAsString();

        } catch (Exception $e) {
            log_message('error', 'GCS Download Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete file from GCS
     * 
     * @param string $gcsPath GCS object path
     * @return bool Success status
     */
    public function deleteFile(string $gcsPath): bool
    {
        try {
            $object = $this->bucket->object($gcsPath);
            
            if ($object->exists()) {
                $object->delete();
                return true;
            }

            return false;

        } catch (Exception $e) {
            log_message('error', 'GCS Delete Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if file exists in GCS
     * 
     * @param string $gcsPath GCS object path
     * @return bool File exists status
     */
    public function fileExists(string $gcsPath): bool
    {
        try {
            $object = $this->bucket->object($gcsPath);
            return $object->exists();

        } catch (Exception $e) {
            log_message('error', 'GCS File Exists Check Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate unique filename
     * 
     * @param string $originalName Original filename
     * @param string $extension File extension
     * @return string Unique filename
     */
    private function generateUniqueFilename(string $originalName, string $extension): string
    {
        $timestamp = time();
        $random = bin2hex(random_bytes(8));
        $safeName = preg_replace('/[^a-zA-Z0-9._-]/', '_', pathinfo($originalName, PATHINFO_FILENAME));
        
        return $safeName . '_' . $timestamp . '_' . $random . '.' . $extension;
    }

    /**
     * Build GCS object path
     * 
     * @param string $category File category
     * @param string $subPath Sub-path
     * @param string $filename Filename
     * @return string GCS object path
     */
    private function buildGcsPath(string $category, string $subPath, string $filename): string
    {
        $path = $category;
        
        if (!empty($subPath)) {
            $path .= '/' . trim($subPath, '/');
        }
        
        return $path . '/' . $filename;
    }

    /**
     * Generate public URL for GCS object
     * 
     * @param string $gcsPath GCS object path
     * @return string Public URL
     */
    private function generatePublicUrl(string $gcsPath): string
    {
        return "https://storage.googleapis.com/{$this->bucketName}/{$gcsPath}";
    }

    /**
     * Get file metadata from GCS
     *
     * @param string $gcsPath GCS object path
     * @return array|false File metadata or false on failure
     */
    public function getFileMetadata(string $gcsPath)
    {
        try {
            $object = $this->bucket->object($gcsPath);

            if (!$object->exists()) {
                return false;
            }

            $info = $object->info();

            return [
                'name' => $info['name'],
                'size' => $info['size'],
                'content_type' => $info['contentType'],
                'created' => $info['timeCreated'],
                'updated' => $info['updated'],
                'metadata' => $info['metadata'] ?? []
            ];

        } catch (Exception $e) {
            log_message('error', 'GCS Metadata Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Test connection to Google Cloud Storage
     *
     * @return array
     */
    public function testConnection(): array
    {
        try {
            // Test if we can initialize storage
            $this->initializeStorage();

            // Test if we can access the bucket
            $bucketInfo = $this->bucket->info();

            return [
                'success' => true,
                'message' => 'Connection successful',
                'bucket_name' => $bucketInfo['name'],
                'bucket_location' => $bucketInfo['location'] ?? 'Unknown'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Copy file from one GCS location to another (creates a new file)
     *
     * @param string $sourceGcsPath Source GCS object path
     * @param string $category Target file category (applicants, applications, etc.)
     * @param string $subPath Optional sub-path within category
     * @return array Copy result with new GCS path and public URL
     */
    public function copyFile(string $sourceGcsPath, string $category, string $subPath = ''): array
    {
        try {
            // Check if source file exists
            $sourceObject = $this->bucket->object($sourceGcsPath);
            if (!$sourceObject->exists()) {
                throw new Exception('Source file not found: ' . $sourceGcsPath);
            }

            // Get source file metadata to preserve original name structure
            $sourceInfo = $sourceObject->info();
            $sourceName = basename($sourceGcsPath);

            // Generate new unique filename while preserving extension
            $pathInfo = pathinfo($sourceName);
            $baseName = $pathInfo['filename'] ?? 'file';
            $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
            $timestamp = time();
            $randomString = bin2hex(random_bytes(8));
            $newFileName = $baseName . '_' . $timestamp . '_' . $randomString . $extension;

            // Build target path
            $targetPath = $category;
            if (!empty($subPath)) {
                $targetPath .= '/' . trim($subPath, '/');
            }
            $targetPath .= '/' . $newFileName;

            // Copy the file to new location
            $targetObject = $sourceObject->copy($this->bucket, [
                'name' => $targetPath
            ]);

            // Generate public URL
            $publicUrl = sprintf(
                'https://storage.googleapis.com/%s/%s',
                $this->bucketName,
                $targetPath
            );

            log_message('info', 'GCS file copied successfully: ' . $sourceGcsPath . ' -> ' . $targetPath);

            return [
                'success' => true,
                'gcs_path' => $targetPath,
                'public_url' => $publicUrl,
                'file_size' => $sourceInfo['size'] ?? 0,
                'content_type' => $sourceInfo['contentType'] ?? 'application/octet-stream'
            ];

        } catch (Exception $e) {
            log_message('error', 'GCS file copy failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
