# Version Bump Guide

This guide helps you bump versions for DERS releases.

## Quick Version Bump Commands

### For PATCH version (bug fixes)
```bash
# Current: 1.1.0 → New: 1.1.1
git add .
git commit -m "fix: [description of fix]"
git tag -a v1.1.1 -m "Release version 1.1.1 - Bug fixes"
git push origin master
git push origin v1.1.1
```

### For MINOR version (new features)
```bash
# Current: 1.1.0 → New: 1.2.0
git add .
git commit -m "feat: [description of new feature]"
git tag -a v1.2.0 -m "Release version 1.2.0 - New features"
git push origin master
git push origin v1.2.0
```

### For MAJOR version (breaking changes)
```bash
# Current: 1.1.0 → New: 2.0.0
git add .
git commit -m "feat!: [description of breaking change]"
git tag -a v2.0.0 -m "Release version 2.0.0 - Major release"
git push origin master
git push origin v2.0.0
```

## Files to Update When Releasing

1. **app/Config/Version.php**
   - Update `VERSION` constant
   - Update `VERSION_NAME` if applicable
   - Update `RELEASE_DATE`
   - Update `BUILD_DATE`

2. **CHANGELOG.md**
   - Move unreleased changes to new version section
   - Add release date
   - Create new unreleased section

3. **VERSIONING.md** (if needed)
   - Update current version status
   - Add to version history

## Release Checklist

- [ ] All tests passing
- [ ] Code review completed
- [ ] Update version in `app/Config/Version.php`
- [ ] Update `CHANGELOG.md`
- [ ] Commit changes with appropriate message
- [ ] Create and push git tag
- [ ] Create GitHub release
- [ ] Update documentation if needed

## Commit Message Conventions

- `fix:` for patch releases (bug fixes)
- `feat:` for minor releases (new features)
- `feat!:` for major releases (breaking changes)
- `docs:` for documentation updates
- `chore:` for maintenance tasks

## Example Release Process

```bash
# 1. Update version files
# Edit app/Config/Version.php
# Edit CHANGELOG.md

# 2. Commit changes
git add .
git commit -m "chore: bump version to 1.2.0"

# 3. Create tag
git tag -a v1.2.0 -m "Release version 1.2.0

New Features:
- Feature 1 description
- Feature 2 description

Bug Fixes:
- Fix 1 description
- Fix 2 description"

# 4. Push everything
git push origin master
git push origin v1.2.0
```

## GitHub Release Creation

1. Go to: https://github.com/anziinols/ders/releases
2. Click "Create a new release"
3. Select the version tag (e.g., v1.2.0)
4. Add release title: "DERS v1.2.0 - [Version Name]"
5. Copy content from CHANGELOG.md for release notes
6. Publish release

## Version History Quick Reference

- **v1.0.0**: Initial stable release
- **v1.1.0**: Application notifications system
- **v1.2.0**: (Planned) Interview management
- **v1.3.0**: (Planned) Advanced reporting
- **v2.0.0**: (Future) Major system upgrade
