<?php

namespace App\Controllers;

use App\Services\GoogleCloudStorageService;
use App\Helpers\FileUploadHelper;
use App\Models\FileUploadsModel;
use App\Config\GoogleCloudStorage;
use CodeIgniter\Files\File;

/**
 * Google Cloud Storage Test Controller
 * 
 * This controller provides testing and demonstration functionality for GCS integration
 */
class GoogleCloudStorageTestController extends BaseController
{
    protected $gcsService;
    protected $fileUploadHelper;
    protected $fileUploadsModel;
    protected $gcsConfig;

    public function __construct()
    {
        $this->gcsConfig = new GoogleCloudStorage();
        $this->fileUploadHelper = new FileUploadHelper();
        $this->fileUploadsModel = new FileUploadsModel();
        
        // Only initialize GCS service if enabled
        if ($this->gcsConfig->enabled) {
            try {
                $this->gcsService = new GoogleCloudStorageService();
            } catch (\Exception $e) {
                log_message('error', 'GCS Service initialization failed: ' . $e->getMessage());
            }
        }
    }

    /**
     * Display GCS test interface
     */
    public function index()
    {
        $data = [
            'title' => 'Google Cloud Storage Test',
            'menu' => 'gcs-test', // Required by dakoiiadmin template
            'gcs_enabled' => $this->gcsConfig->enabled,
            'gcs_config' => $this->gcsConfig,
            'connection_test' => $this->gcsService->testConnection(),
            'validation' => $this->gcsConfig->validateConfig(),
            'file_stats' => $this->fileUploadsModel->getFileStatistics()
        ];

        return view('gcs_test/index', $data);
    }

    /**
     * Test file upload
     */
    public function testUpload()
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->back()->with('error', 'Invalid request method');
        }

        $file = $this->request->getFile('test_file');
        $category = $this->request->getPost('category') ?: 'applicants';
        $subPath = $this->request->getPost('sub_path') ?: '';

        if (!$file || !$file->isValid()) {
            return redirect()->back()->with('error', 'No valid file uploaded');
        }

        try {
            // Upload file using FileUploadHelper
            $result = $this->fileUploadHelper->uploadFile(
                $file, 
                $category, 
                $subPath, 
                session()->get('user_id')
            );

            if ($result['success']) {
                // Record upload in database
                $uploadId = $this->fileUploadsModel->recordUpload(
                    $result, 
                    'test_uploads', 
                    null
                );

                $message = "File uploaded successfully! ";
                $message .= "Storage: " . $result['storage_type'] . ", ";
                $message .= "Path: " . ($result['gcs_path'] ?? $result['file_path']);

                return redirect()->back()->with('success', $message);
            } else {
                return redirect()->back()->with('error', 'Upload failed: ' . $result['error']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Test upload error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Upload error: ' . $e->getMessage());
        }
    }

    /**
     * List uploaded files
     */
    public function listFiles()
    {
        $category = $this->request->getGet('category');
        $storageType = $this->request->getGet('storage_type');

        $builder = $this->fileUploadsModel->where('is_active', 1);

        if ($category) {
            $builder->where('category', $category);
        }

        if ($storageType) {
            $builder->where('storage_type', $storageType);
        }

        $files = $builder->orderBy('created_at', 'DESC')->findAll();

        $data = [
            'title' => 'Uploaded Files',
            'menu' => 'gcs-test', // Required by dakoiiadmin template
            'files' => $files,
            'categories' => array_keys($this->gcsConfig->categories),
            'current_category' => $category,
            'current_storage_type' => $storageType
        ];

        return view('gcs_test/list_files', $data);
    }

    /**
     * Download file
     */
    public function downloadFile($fileId)
    {
        $file = $this->fileUploadsModel->find($fileId);

        if (!$file) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('File not found');
        }

        try {
            if ($file['storage_type'] === 'gcs' && $this->gcsService) {
                // Download from GCS
                $content = $this->gcsService->downloadFile($file['file_path']);
                
                if ($content === false) {
                    throw new \Exception('Failed to download file from GCS');
                }

                return $this->response
                    ->setHeader('Content-Type', $file['mime_type'])
                    ->setHeader('Content-Disposition', 'attachment; filename="' . $file['original_filename'] . '"')
                    ->setBody($content);

            } else {
                // Download from local storage
                $filePath = FCPATH . $file['file_path'];
                
                if (!file_exists($filePath)) {
                    throw new \Exception('Local file not found');
                }

                return $this->response->download($filePath, null);
            }

        } catch (\Exception $e) {
            log_message('error', 'File download error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Download failed: ' . $e->getMessage());
        }
    }

    /**
     * Delete file
     */
    public function deleteFile($fileId)
    {
        $file = $this->fileUploadsModel->find($fileId);

        if (!$file) {
            return redirect()->back()->with('error', 'File not found');
        }

        try {
            // Delete from storage
            $deleted = $this->fileUploadHelper->deleteFile(
                $file['file_path'], 
                $file['storage_type']
            );

            if ($deleted) {
                // Mark as inactive in database
                $this->fileUploadsModel->deactivateFile($fileId);
                return redirect()->back()->with('success', 'File deleted successfully');
            } else {
                return redirect()->back()->with('error', 'Failed to delete file from storage');
            }

        } catch (\Exception $e) {
            log_message('error', 'File deletion error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Deletion failed: ' . $e->getMessage());
        }
    }

    /**
     * Test GCS connection
     */
    public function testConnection()
    {
        if (!$this->gcsConfig->enabled) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Google Cloud Storage is not enabled'
            ]);
        }

        try {
            if (!$this->gcsService) {
                throw new \Exception('GCS Service not initialized');
            }

            // Try to list bucket contents (limited)
            $bucket = $this->gcsService->bucket ?? null;
            
            if ($bucket) {
                // Simple test - check if bucket exists
                $exists = $bucket->exists();
                
                return $this->response->setJSON([
                    'success' => $exists,
                    'message' => $exists ? 'GCS connection successful' : 'Bucket not found'
                ]);
            } else {
                throw new \Exception('Bucket not accessible');
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get file statistics
     */
    public function getStats()
    {
        $stats = $this->fileUploadsModel->getFileStatistics();
        
        return $this->response->setJSON([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Migrate local files to GCS
     */
    public function migrateToGcs()
    {
        if (!$this->gcsConfig->enabled || !$this->gcsService) {
            return redirect()->back()->with('error', 'GCS is not enabled or configured');
        }

        $localFiles = $this->fileUploadsModel->getFilesByStorageType('local');
        $migrated = 0;
        $errors = 0;

        foreach ($localFiles as $file) {
            try {
                $localPath = FCPATH . $file['file_path'];
                
                if (!file_exists($localPath)) {
                    $errors++;
                    continue;
                }

                // Upload to GCS
                $result = $this->gcsService->uploadFromPath(
                    $localPath,
                    $file['category'],
                    $file['sub_path'],
                    $file['original_filename']
                );

                if ($result['success']) {
                    // Update database record
                    $this->fileUploadsModel->update($file['id'], [
                        'file_path' => $result['gcs_path'],
                        'storage_type' => 'gcs',
                        'public_url' => $result['public_url']
                    ]);

                    // Optionally delete local file
                    // unlink($localPath);
                    
                    $migrated++;
                } else {
                    $errors++;
                    log_message('error', 'Migration failed for file ID ' . $file['id'] . ': ' . $result['error']);
                }

            } catch (\Exception $e) {
                $errors++;
                log_message('error', 'Migration error for file ID ' . $file['id'] . ': ' . $e->getMessage());
            }
        }

        $message = "Migration completed. Migrated: {$migrated}, Errors: {$errors}";
        return redirect()->back()->with('success', $message);
    }
}
