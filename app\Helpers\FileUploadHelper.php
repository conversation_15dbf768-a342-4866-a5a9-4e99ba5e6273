<?php

namespace App\Helpers;

use App\Services\GoogleCloudStorageService;
use App\Config\GoogleCloudStorage;
use App\Models\FileUploadsModel;
use CodeIgniter\Files\File;
use Exception;

/**
 * File Upload Helper
 * 
 * Handles file uploads with support for both local storage and Google Cloud Storage
 */
class FileUploadHelper
{
    private $gcsService;
    private $gcsConfig;
    private $useGcs;

    public function __construct()
    {
        $this->gcsConfig = new GoogleCloudStorage();
        $this->useGcs = $this->gcsConfig->enabled;

        if ($this->useGcs) {
            try {
                $this->gcsService = new GoogleCloudStorageService();
            } catch (Exception $e) {
                log_message('error', 'Failed to initialize GCS, falling back to local storage: ' . $e->getMessage());
                $this->useGcs = false;
            }
        }
    }

    /**
     * Upload file with automatic storage selection
     * 
     * @param File $file The file to upload
     * @param string $category File category
     * @param string $subPath Optional sub-path
     * @param int|null $userId User ID for tracking
     * @return array Upload result
     */
    public function uploadFile(File $file, string $category, string $subPath = '', ?int $userId = null): array
    {
        try {
            // Validate file
            $validation = $this->validateFile($file, $category);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => implode(', ', $validation['errors'])
                ];
            }

            // Upload to appropriate storage
            if ($this->useGcs) {
                $result = $this->uploadToGcs($file, $category, $subPath);
            } else {
                $result = $this->uploadToLocal($file, $category, $subPath);
            }

            // Add metadata
            if ($result['success']) {
                $result['storage_type'] = $this->useGcs ? 'gcs' : 'local';
                $result['uploaded_by'] = $userId;
                $result['uploaded_at'] = date('Y-m-d H:i:s');
                $result['category'] = $category;
                $result['sub_path'] = $subPath;
            }

            return $result;

        } catch (Exception $e) {
            log_message('error', 'File upload error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Upload failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Upload multiple files
     * 
     * @param array $files Array of File objects
     * @param string $category File category
     * @param string $subPath Optional sub-path
     * @param int|null $userId User ID for tracking
     * @return array Upload results
     */
    public function uploadMultipleFiles(array $files, string $category, string $subPath = '', ?int $userId = null): array
    {
        $results = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($files as $index => $file) {
            if ($file instanceof File) {
                $result = $this->uploadFile($file, $category, $subPath, $userId);
                $results[$index] = $result;

                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            } else {
                $results[$index] = [
                    'success' => false,
                    'error' => 'Invalid file object'
                ];
                $errorCount++;
            }
        }

        return [
            'results' => $results,
            'summary' => [
                'total' => count($files),
                'success' => $successCount,
                'errors' => $errorCount
            ]
        ];
    }

    /**
     * Delete file from storage
     * 
     * @param string $filePath File path (local or GCS)
     * @param string $storageType Storage type ('local' or 'gcs')
     * @return bool Success status
     */
    public function deleteFile(string $filePath, string $storageType = 'auto'): bool
    {
        try {
            if ($storageType === 'auto') {
                $storageType = $this->detectStorageType($filePath);
            }

            if ($storageType === 'gcs' && $this->useGcs) {
                return $this->gcsService->deleteFile($filePath);
            } else {
                return $this->deleteLocalFile($filePath);
            }

        } catch (Exception $e) {
            log_message('error', 'File deletion error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get file URL for display/download
     * 
     * @param string $filePath File path
     * @param string $storageType Storage type
     * @return string|null File URL
     */
    public function getFileUrl(string $filePath, string $storageType = 'auto'): ?string
    {
        if ($storageType === 'auto') {
            $storageType = $this->detectStorageType($filePath);
        }

        if ($storageType === 'gcs') {
            return "https://storage.googleapis.com/{$this->gcsConfig->bucketName}/{$filePath}";
        } else {
            return base_url($filePath);
        }
    }

    /**
     * Check if file exists
     * 
     * @param string $filePath File path
     * @param string $storageType Storage type
     * @return bool File exists status
     */
    public function fileExists(string $filePath, string $storageType = 'auto'): bool
    {
        if ($storageType === 'auto') {
            $storageType = $this->detectStorageType($filePath);
        }

        if ($storageType === 'gcs' && $this->useGcs) {
            return $this->gcsService->fileExists($filePath);
        } else {
            return file_exists($filePath);
        }
    }

    /**
     * Validate file before upload
     * 
     * @param File $file File to validate
     * @param string $category File category
     * @return array Validation result
     */
    private function validateFile(File $file, string $category): array
    {
        $errors = [];

        // Check if category exists
        $categoryConfig = $this->gcsConfig->getCategoryConfig($category);
        if (!$categoryConfig) {
            $errors[] = "Invalid file category: {$category}";
            return ['valid' => false, 'errors' => $errors];
        }

        // Check file extension
        $extension = $file->getClientExtension();
        if (!$this->gcsConfig->isFileTypeAllowed($category, $extension)) {
            $errors[] = "File type '{$extension}' not allowed for category '{$category}'";
        }

        // Check file size
        $fileSize = $file->getSize();
        if (!$this->gcsConfig->isFileSizeAllowed($category, $fileSize)) {
            $maxSize = $categoryConfig['max_size'];
            $errors[] = "File size exceeds limit. Maximum allowed: " . $this->formatBytes($maxSize);
        }

        // Check MIME type
        $mimeType = $file->getMimeType();
        if (!in_array($mimeType, $this->gcsConfig->security['allowed_mime_types'])) {
            $errors[] = "MIME type '{$mimeType}' not allowed";
        }

        // Additional security checks
        if ($this->gcsConfig->security['check_file_headers']) {
            if (!$this->validateFileHeaders($file)) {
                $errors[] = "File failed security validation";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Upload file to Google Cloud Storage
     * 
     * @param File $file File to upload
     * @param string $category File category
     * @param string $subPath Sub-path
     * @return array Upload result
     */
    private function uploadToGcs(File $file, string $category, string $subPath): array
    {
        return $this->gcsService->uploadFile($file, $category, $subPath);
    }

    /**
     * Upload file to local storage
     * 
     * @param File $file File to upload
     * @param string $category File category
     * @param string $subPath Sub-path
     * @return array Upload result
     */
    private function uploadToLocal(File $file, string $category, string $subPath): array
    {
        try {
            $categoryConfig = $this->gcsConfig->getCategoryConfig($category);
            $basePath = $this->gcsConfig->localFallback['base_path'];
            
            // Build local path
            $uploadPath = $basePath . $categoryConfig['path'];
            if (!empty($subPath)) {
                $uploadPath .= '/' . trim($subPath, '/');
            }

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Generate unique filename
            $originalName = $file->getClientName();
            $extension = $file->getClientExtension();
            $filename = $this->generateUniqueFilename($originalName, $extension);

            // Move file
            $file->move($uploadPath, $filename);
            
            $filePath = $uploadPath . '/' . $filename;
            $relativePath = str_replace(FCPATH, '', $filePath);

            return [
                'success' => true,
                'file_path' => $relativePath,
                'public_url' => base_url($relativePath),
                'filename' => $filename,
                'original_name' => $originalName,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete local file
     * 
     * @param string $filePath Local file path
     * @return bool Success status
     */
    private function deleteLocalFile(string $filePath): bool
    {
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return false;
    }

    /**
     * Detect storage type from file path
     * 
     * @param string $filePath File path
     * @return string Storage type
     */
    private function detectStorageType(string $filePath): string
    {
        // If path starts with category name and doesn't contain 'public/', it's likely GCS
        $categories = array_keys($this->gcsConfig->categories);
        $pathStart = explode('/', $filePath)[0];
        
        if (in_array($pathStart, $categories) && strpos($filePath, 'public/') === false) {
            return 'gcs';
        }
        
        return 'local';
    }

    /**
     * Generate unique filename
     * 
     * @param string $originalName Original filename
     * @param string $extension File extension
     * @return string Unique filename
     */
    private function generateUniqueFilename(string $originalName, string $extension): string
    {
        $timestamp = time();
        $random = bin2hex(random_bytes(8));
        $safeName = preg_replace('/[^a-zA-Z0-9._-]/', '_', pathinfo($originalName, PATHINFO_FILENAME));
        
        return $safeName . '_' . $timestamp . '_' . $random . '.' . $extension;
    }

    /**
     * Validate file headers for security
     * 
     * @param File $file File to validate
     * @return bool Validation result
     */
    private function validateFileHeaders(File $file): bool
    {
        // Basic file header validation
        // This is a simplified version - implement more robust validation as needed
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file->getTempName());
        finfo_close($finfo);

        return $mimeType === $file->getMimeType();
    }

    /**
     * Format bytes to human readable format
     * 
     * @param int $bytes Bytes
     * @return string Formatted size
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
}
