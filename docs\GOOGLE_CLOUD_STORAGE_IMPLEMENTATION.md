# Google Cloud Storage Implementation Guide

## Overview

This document describes the implementation of Google Cloud Storage (GCS) integration in the DERS application, providing seamless file upload and management capabilities with fallback to local storage.

## Features Implemented

### ✅ Core Components

1. **GoogleCloudStorageService** - Main service for GCS operations
2. **FileUploadHelper** - Unified file upload interface with automatic storage selection
3. **FileUploadsModel** - Database tracking for all file uploads
4. **GoogleCloudStorage Config** - Comprehensive configuration management
5. **GCS Test Controller** - Testing and demonstration interface

### ✅ Key Capabilities

- **Dual Storage Support**: Automatic fallback from GCS to local storage
- **File Validation**: Comprehensive security and format validation
- **Metadata Tracking**: Complete audit trail for all uploads
- **Migration Tools**: Migrate existing local files to GCS
- **Test Interface**: Web-based testing and management tools

## File Structure

```
app/
├── Config/
│   └── GoogleCloudStorage.php          # GCS configuration
├── Controllers/
│   └── GoogleCloudStorageTestController.php  # Test interface
├── Database/Migrations/
│   └── 2024-12-14-000001_AddGcsFieldsToTables.php  # Database schema
├── Helpers/
│   └── FileUploadHelper.php            # Unified upload interface
├── Models/
│   └── FileUploadsModel.php            # File tracking model
├── Services/
│   └── GoogleCloudStorageService.php   # Core GCS service
└── Views/gcs_test/
    └── index.php                       # Test interface view
```

## Configuration

### Environment Variables (.env)

```env
# Enable Google Cloud Storage
GCS_ENABLED = true

# Google Cloud Project ID
GCS_PROJECT_ID = your-project-id

# Google Cloud Storage Bucket Name
GCS_BUCKET_NAME = ders-files-production

# Path to Service Account Key File
GCS_KEY_FILE_PATH = path/to/service-account-key.json
```

### Google Cloud Setup

1. **Create Service Account**
   ```bash
   gcloud iam service-accounts create ders-storage \
       --display-name="DERS Storage Service"
   ```

2. **Grant Permissions**
   ```bash
   gcloud projects add-iam-policy-binding PROJECT_ID \
       --member="serviceAccount:ders-storage@PROJECT_ID.iam.gserviceaccount.com" \
       --role="roles/storage.admin"
   ```

3. **Create Bucket**
   ```bash
   gsutil mb gs://ders-files-production
   ```

4. **Generate Key File**
   ```bash
   gcloud iam service-accounts keys create service-account-key.json \
       --iam-account=ders-storage@PROJECT_ID.iam.gserviceaccount.com
   ```

## Usage Examples

### Basic File Upload

```php
use App\Helpers\FileUploadHelper;

$fileHelper = new FileUploadHelper();

// Upload file with automatic storage selection
$result = $fileHelper->uploadFile(
    $file,           // CodeIgniter File object
    'applicants',    // Category
    'user123',       // Sub-path (optional)
    $userId          // User ID for tracking
);

if ($result['success']) {
    echo "File uploaded: " . $result['public_url'];
} else {
    echo "Upload failed: " . $result['error'];
}
```

### Direct GCS Service Usage

```php
use App\Services\GoogleCloudStorageService;

$gcsService = new GoogleCloudStorageService();

// Upload file directly to GCS
$result = $gcsService->uploadFile($file, 'documents', 'subfolder');

// Download file content
$content = $gcsService->downloadFile('documents/subfolder/filename.pdf');

// Delete file
$deleted = $gcsService->deleteFile('documents/subfolder/filename.pdf');
```

### File Tracking

```php
use App\Models\FileUploadsModel;

$fileModel = new FileUploadsModel();

// Record upload in database
$uploadId = $fileModel->recordUpload($uploadResult, 'applicants', $applicantId);

// Get files by category
$files = $fileModel->getFilesByCategory('applicants');

// Get files for specific record
$files = $fileModel->getFilesByRelated('applicants', $applicantId);
```

## Database Schema

### New Tables

#### file_uploads
Tracks all file uploads with metadata:
- `id` - Primary key
- `original_filename` - Original file name
- `stored_filename` - Generated unique filename
- `file_path` - Storage path (local or GCS)
- `storage_type` - 'local' or 'gcs'
- `category` - File category
- `file_size` - File size in bytes
- `mime_type` - MIME type
- `public_url` - Public access URL
- `uploaded_by` - User ID
- `related_table` - Related table name
- `related_id` - Related record ID
- `metadata` - JSON metadata
- `is_active` - Active status
- Timestamps and soft deletes

### Enhanced Tables

Added GCS-related fields to existing tables:
- **applicants**: `profile_photo_gcs_path`, `profile_photo_storage_type`
- **appx_application_details**: `cv_gcs_path`, `cover_letter_gcs_path`, `certificates_gcs_path`, `documents_storage_type`
- **dakoii_org**: `signature_gcs_path`, `logo_gcs_path`, `org_storage_type`
- **positions**: `job_description_gcs_path`, `attachments_gcs_path`, `position_storage_type`

## File Categories

Configured categories with validation rules:

1. **applicants** - Applicant documents and photos (10MB, PDF/DOC/Images)
2. **applications** - Application documents (10MB, PDF/DOC)
3. **org_signatures** - Organization signatures (2MB, Images)
4. **job_descriptions** - Job description documents (5MB, PDF/DOC)
5. **profile_photos** - Profile photos (2MB, Images)
6. **cv_documents** - CV and resume documents (10MB, PDF/DOC)
7. **certificates** - Educational certificates (10MB, PDF/Images)
8. **cover_letters** - Cover letter documents (5MB, PDF/DOC)

## Security Features

### File Validation
- File type validation by extension and MIME type
- File size limits per category and global
- File header validation for security
- Blocked executable file types

### Access Control
- User-based upload tracking
- Role-based access to test interface
- Secure file URLs with proper authentication

## Testing Interface

Access the GCS test interface at: `/gcs-test`

### Features:
- **Configuration Status** - View current GCS setup
- **Connection Testing** - Test GCS connectivity
- **File Upload Testing** - Upload test files
- **File Management** - View, download, delete files
- **Statistics** - File usage statistics
- **Migration Tools** - Migrate local files to GCS

## Migration Strategy

### Gradual Migration
1. **Phase 1**: Install and configure GCS (current)
2. **Phase 2**: Test with new uploads
3. **Phase 3**: Migrate existing files
4. **Phase 4**: Full GCS deployment

### Migration Commands
```php
// Migrate all local files to GCS
$controller = new GoogleCloudStorageTestController();
$result = $controller->migrateToGcs();
```

## Monitoring and Maintenance

### File Statistics
- Total files and storage usage
- Local vs GCS distribution
- Category breakdown
- Upload trends

### Cleanup Operations
- Orphaned file detection
- Inactive file cleanup
- Storage optimization

## Error Handling

### Fallback Mechanism
- Automatic fallback to local storage if GCS fails
- Graceful error handling with user feedback
- Comprehensive logging for troubleshooting

### Common Issues
1. **Service Account Key** - Ensure proper permissions and file path
2. **Bucket Access** - Verify bucket exists and permissions
3. **Network Issues** - Check connectivity and firewall settings
4. **File Size Limits** - Adjust limits in configuration

## Performance Considerations

### Optimization Features
- Chunked uploads for large files
- Concurrent upload support
- Metadata caching
- Retry mechanisms

### Best Practices
- Use appropriate file categories
- Implement proper cleanup routines
- Monitor storage usage
- Regular backup verification

## Future Enhancements

### Planned Features
- CDN integration for faster delivery
- Image resizing and optimization
- Virus scanning integration
- Advanced analytics and reporting
- Automated backup and archival

## Support and Troubleshooting

### Logs
Check CodeIgniter logs for GCS-related errors:
```
writable/logs/log-YYYY-MM-DD.php
```

### Debug Mode
Enable debug mode in test interface for detailed error information.

### Common Solutions
1. **Permission Denied**: Check service account permissions
2. **Bucket Not Found**: Verify bucket name and existence
3. **Upload Fails**: Check file size and type restrictions
4. **Connection Timeout**: Increase timeout settings

---

**Note**: This implementation provides a robust, scalable file storage solution with comprehensive testing and migration tools. The dual storage approach ensures reliability while enabling gradual migration to cloud storage.
