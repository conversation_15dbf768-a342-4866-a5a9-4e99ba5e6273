<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationFilesModel
 *
 * Model for the appx_application_files table
 */
class AppxApplicationFilesModel extends Model
{
    protected $table         = 'appx_application_files';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'applicant_id',
        'applicant_file_id',
        'file_title',
        'file_description',
        'file_path',
        'gcs_path',
        'storage_type',
        'public_url',
        'extracted_texts',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'applicant_id'   => 'permit_empty|numeric',
        'file_title'     => 'required|max_length[255]',
        'file_path'      => 'permit_empty|max_length[255]'
    ];

    protected $validationMessages = [
        'applicant_id' => [
            'numeric'  => 'Applicant ID must be a number'
        ],
        'file_title' => [
            'required'    => 'File title is required',
            'max_length'  => 'File title cannot exceed 255 characters'
        ],
        'file_path' => [
            'max_length'  => 'File path cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get files by application ID using junction table
     *
     * @param int $applicationId
     * @return array
     */
    public function getFilesByApplicationId($applicationId)
    {
        return $this->select('appx_application_files.*')
                   ->join('appx_application_file_links', 'appx_application_files.id = appx_application_file_links.file_id')
                   ->where('appx_application_file_links.application_id', $applicationId)
                   ->findAll();
    }

    /**
     * Get files by applicant ID
     *
     * @param int $applicantId
     * @return array
     */
    public function getFilesByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)->findAll();
    }

    /**
     * Get file by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getFileById($id)
    {
        return $this->find($id);
    }

    /**
     * Get files with extracted text for an application
     *
     * @param int $applicationId
     * @return array
     */
    public function getFilesWithExtractedText($applicationId)
    {
        return $this->select('appx_application_files.*')
                   ->join('appx_application_file_links', 'appx_application_files.id = appx_application_file_links.file_id')
                   ->where('appx_application_file_links.application_id', $applicationId)
                   ->where('appx_application_files.extracted_texts IS NOT NULL')
                   ->where('appx_application_files.extracted_texts !=', '')
                   ->findAll();
    }

    /**
     * Search files by title
     *
     * @param string $search
     * @param int $applicationId
     * @return array
     */
    public function searchFilesByTitle($search, $applicationId = null)
    {
        if ($applicationId !== null) {
            return $this->select('appx_application_files.*')
                       ->join('appx_application_file_links', 'appx_application_files.id = appx_application_file_links.file_id')
                       ->where('appx_application_file_links.application_id', $applicationId)
                       ->like('appx_application_files.file_title', $search)
                       ->findAll();
        }

        return $this->like('file_title', $search)->findAll();
    }

    /**
     * Get file count by application
     *
     * @param int $applicationId
     * @return int
     */
    public function getFileCountByApplication($applicationId)
    {
        return $this->select('appx_application_files.id')
                   ->join('appx_application_file_links', 'appx_application_files.id = appx_application_file_links.file_id')
                   ->where('appx_application_file_links.application_id', $applicationId)
                   ->countAllResults();
    }

    /**
     * Remove file links for an application (doesn't delete the actual files)
     *
     * @param int $applicationId
     * @return bool
     */
    public function removeFileLinksForApplication($applicationId)
    {
        $db = \Config\Database::connect();
        return $db->table('appx_application_file_links')
                  ->where('application_id', $applicationId)
                  ->delete();
    }

    /**
     * Get file URL based on storage type
     *
     * @param array $file
     * @return string
     */
    public function getFileUrl(array $file): string
    {
        // Check for GCS storage first
        if (isset($file['storage_type']) && $file['storage_type'] === 'gcs') {
            return $file['public_url'] ?? '';
        }

        // Fallback to local file path
        if (!empty($file['file_path'])) {
            return base_url($file['file_path']);
        }

        return '#';
    }

    /**
     * Get files for an application with enhanced data
     *
     * @param int $applicationId
     * @return array
     */
    public function getFilesWithUrls(int $applicationId): array
    {
        $files = $this->select('appx_application_files.*')
                     ->join('appx_application_file_links', 'appx_application_files.id = appx_application_file_links.file_id')
                     ->where('appx_application_file_links.application_id', $applicationId)
                     ->findAll();

        foreach ($files as &$file) {
            $file['view_url'] = $this->getFileUrl($file);
            $file['storage_display'] = (isset($file['storage_type']) && $file['storage_type'] === 'gcs') ? 'Cloud Storage' : 'Local Storage';
        }

        return $files;
    }

    /**
     * Check if a file already exists in the repository by applicant_file_id
     *
     * @param int $applicantFileId
     * @return array|null Returns the existing file record or null
     */
    public function getExistingFile(int $applicantFileId): ?array
    {
        return $this->where('applicant_file_id', $applicantFileId)->first();
    }

    /**
     * Check if applicant_file_id exists in the repository
     *
     * @param int $applicantFileId
     * @return bool
     */
    public function applicantFileIdExists(int $applicantFileId): bool
    {
        return $this->where('applicant_file_id', $applicantFileId)->countAllResults() > 0;
    }

    /**
     * Check if a file is already linked to an application
     *
     * @param int $applicationId
     * @param int $fileId
     * @return bool
     */
    public function isFileLinkedToApplication(int $applicationId, int $fileId): bool
    {
        $db = \Config\Database::connect();
        return $db->table('appx_application_file_links')
                  ->where('application_id', $applicationId)
                  ->where('file_id', $fileId)
                  ->countAllResults() > 0;
    }

    /**
     * Link a file to an application
     *
     * @param int $applicationId
     * @param int $fileId
     * @return bool
     */
    public function linkFileToApplication(int $applicationId, int $fileId): bool
    {
        $db = \Config\Database::connect();
        return $db->table('appx_application_file_links')
                  ->insert([
                      'application_id' => $applicationId,
                      'file_id' => $fileId,
                      'created_at' => date('Y-m-d H:i:s')
                  ]);
    }
}
