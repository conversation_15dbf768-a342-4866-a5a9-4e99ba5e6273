<?php

namespace App\Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Google Cloud Storage Configuration
 */
class GoogleCloudStorage extends BaseConfig
{
    /**
     * Google Cloud Project ID
     */
    public string $projectId;

    /**
     * Google Cloud Storage Bucket Name
     */
    public string $bucketName;

    /**
     * Path to Google Cloud Service Account Key File
     */
    public string $keyFilePath;

    /**
     * Enable/Disable Google Cloud Storage
     * Set to false to use local storage as fallback
     */
    public bool $enabled;

    public function __construct()
    {
        parent::__construct();

        // Load from environment variables
        $this->projectId = env('GCS_PROJECT_ID', '');
        $this->bucketName = env('GCS_BUCKET_NAME', '');
        $this->keyFilePath = env('GCS_KEY_FILE_PATH', '');
        $this->enabled = env('GCS_ENABLED', false);
    }

    /**
     * File upload categories and their configurations
     */
    public array $categories = [
        'applicants' => [
            'path' => 'applicants',
            'max_size' => ********, // 10MB
            'allowed_types' => ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
            'description' => 'Applicant documents and photos'
        ],
        'applications' => [
            'path' => 'applications',
            'max_size' => ********, // 10MB
            'allowed_types' => ['pdf', 'doc', 'docx'],
            'description' => 'Application documents'
        ],
        'org_signatures' => [
            'path' => 'org_signatures',
            'max_size' => 2097152, // 2MB
            'allowed_types' => ['jpg', 'jpeg', 'png', 'gif'],
            'description' => 'Organization signature images'
        ],
        'job_descriptions' => [
            'path' => 'job_descriptions',
            'max_size' => 5242880, // 5MB
            'allowed_types' => ['pdf', 'doc', 'docx'],
            'description' => 'Job description documents'
        ],
        'profile_photos' => [
            'path' => 'profile_photos',
            'max_size' => 2097152, // 2MB
            'allowed_types' => ['jpg', 'jpeg', 'png'],
            'description' => 'Profile photos'
        ],
        'cv_documents' => [
            'path' => 'cv_documents',
            'max_size' => ********, // 10MB
            'allowed_types' => ['pdf', 'doc', 'docx'],
            'description' => 'CV and resume documents'
        ],
        'certificates' => [
            'path' => 'certificates',
            'max_size' => ********, // 10MB
            'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
            'description' => 'Educational and professional certificates'
        ],
        'cover_letters' => [
            'path' => 'cover_letters',
            'max_size' => 5242880, // 5MB
            'allowed_types' => ['pdf', 'doc', 'docx'],
            'description' => 'Cover letter documents'
        ]
    ];

    /**
     * Local storage fallback configuration
     */
    public array $localFallback = [
        'enabled' => true,
        'base_path' => 'public/uploads/',
        'create_directories' => true
    ];

    /**
     * File naming configuration
     */
    public array $fileNaming = [
        'use_timestamp' => true,
        'use_random_suffix' => true,
        'preserve_original_name' => true,
        'max_filename_length' => 255
    ];

    /**
     * Security settings
     */
    public array $security = [
        'scan_for_malware' => false, // Enable if malware scanning service is available
        'check_file_headers' => true,
        'block_executable_files' => true,
        'max_upload_size' => 20971520, // 20MB global limit
        'allowed_mime_types' => [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
            'image/gif'
        ]
    ];

    /**
     * Performance settings
     */
    public array $performance = [
        'chunk_size' => 1048576, // 1MB chunks for large file uploads
        'timeout' => 300, // 5 minutes timeout
        'retry_attempts' => 3,
        'concurrent_uploads' => 5
    ];

    /**
     * Logging configuration
     */
    public array $logging = [
        'log_uploads' => true,
        'log_downloads' => true,
        'log_deletions' => true,
        'log_errors' => true,
        'log_level' => 'info'
    ];

    /**
     * Cache settings for file metadata
     */
    public array $cache = [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'prefix' => 'gcs_'
    ];

    /**
     * Get category configuration
     * 
     * @param string $category
     * @return array|null
     */
    public function getCategoryConfig(string $category): ?array
    {
        return $this->categories[$category] ?? null;
    }

    /**
     * Check if file type is allowed for category
     * 
     * @param string $category
     * @param string $extension
     * @return bool
     */
    public function isFileTypeAllowed(string $category, string $extension): bool
    {
        $config = $this->getCategoryConfig($category);
        
        if (!$config) {
            return false;
        }

        return in_array(strtolower($extension), $config['allowed_types']);
    }

    /**
     * Check if file size is within limits for category
     * 
     * @param string $category
     * @param int $fileSize
     * @return bool
     */
    public function isFileSizeAllowed(string $category, int $fileSize): bool
    {
        $config = $this->getCategoryConfig($category);
        
        if (!$config) {
            return false;
        }

        return $fileSize <= $config['max_size'] && $fileSize <= $this->security['max_upload_size'];
    }

    /**
     * Get all allowed file types
     * 
     * @return array
     */
    public function getAllAllowedTypes(): array
    {
        $types = [];
        
        foreach ($this->categories as $category) {
            $types = array_merge($types, $category['allowed_types']);
        }

        return array_unique($types);
    }

    /**
     * Validate configuration
     * 
     * @return array Validation results
     */
    public function validateConfig(): array
    {
        $errors = [];

        if ($this->enabled) {
            if (empty($this->projectId)) {
                $errors[] = 'Google Cloud Project ID is required';
            }

            if (empty($this->bucketName)) {
                $errors[] = 'Google Cloud Storage Bucket Name is required';
            }

            if (empty($this->keyFilePath)) {
                $errors[] = 'Google Cloud Service Account Key File Path is required';
            } else {
                // Convert relative path to absolute path
                $keyFilePath = $this->keyFilePath;
                if ($keyFilePath && !str_starts_with($keyFilePath, '/') && !str_contains($keyFilePath, ':\\')) {
                    $keyFilePath = ROOTPATH . $keyFilePath;
                }

                if (!file_exists($keyFilePath)) {
                    $errors[] = 'Google Cloud Service Account Key File not found: ' . $keyFilePath;
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
