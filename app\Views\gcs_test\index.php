<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cloud-upload-alt me-2"></i>
                        Google Cloud Storage Test Interface
                    </h3>
                </div>
                <div class="card-body">
                    
                    <!-- Configuration Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card <?= $gcs_enabled ? 'border-success' : 'border-warning' ?>">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-cog me-2"></i>
                                        Configuration Status
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>GCS Enabled:</strong> 
                                        <span class="badge <?= $gcs_enabled ? 'bg-success' : 'bg-warning' ?>">
                                            <?= $gcs_enabled ? 'Yes' : 'No' ?>
                                        </span>
                                    </div>
                                    
                                    <?php if ($gcs_enabled): ?>
                                        <div class="mb-2">
                                            <strong>Project ID:</strong> <?= esc($gcs_config->projectId ?: 'Not set') ?>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Bucket Name:</strong> <?= esc($gcs_config->bucketName ?: 'Not set') ?>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Key File:</strong>
                                            <?php
                                            $keyFilePath = $gcs_config->keyFilePath;
                                            if ($keyFilePath && !str_starts_with($keyFilePath, '/') && !str_contains($keyFilePath, ':\\')) {
                                                $keyFilePath = ROOTPATH . $keyFilePath;
                                            }
                                            $keyFileExists = $keyFilePath && file_exists($keyFilePath);
                                            ?>
                                            <span class="badge <?= $keyFileExists ? 'bg-success' : 'bg-danger' ?>">
                                                <?= $keyFileExists ? 'Found' : 'Not found' ?>
                                            </span>
                                        </div>

                                        <!-- Connection Test -->
                                        <div class="mb-2">
                                            <strong>Connection Status:</strong>
                                            <span class="badge <?= $connection_test['success'] ? 'bg-success' : 'bg-danger' ?>">
                                                <?= $connection_test['success'] ? 'Connected' : 'Failed' ?>
                                            </span>
                                        </div>

                                        <?php if ($connection_test['success']): ?>
                                            <div class="mb-2">
                                                <strong>Bucket Location:</strong> <?= esc($connection_test['bucket_location'] ?? 'Unknown') ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-warning mt-2 mb-0">
                                                <small><?= esc($connection_test['message']) ?></small>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <!-- Validation Results -->
                                    <?php if (!$validation['valid']): ?>
                                        <div class="alert alert-danger mt-3">
                                            <strong>Configuration Errors:</strong>
                                            <ul class="mb-0">
                                                <?php foreach ($validation['errors'] as $error): ?>
                                                    <li><?= esc($error) ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Test Connection Button -->
                                    <?php if ($gcs_enabled && $validation['valid']): ?>
                                        <button type="button" class="btn btn-primary btn-sm mt-2" onclick="testConnection()">
                                            <i class="fas fa-plug me-1"></i>
                                            Test Connection
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        File Statistics
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-primary"><?= number_format($file_stats['total_files']) ?></h4>
                                                <small class="text-muted">Total Files</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-info"><?= formatBytes($file_stats['total_size']) ?></h4>
                                                <small class="text-muted">Total Size</small>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h5 class="text-success"><?= number_format($file_stats['local_files']) ?></h5>
                                                <small class="text-muted">Local Files</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h5 class="text-warning"><?= number_format($file_stats['gcs_files']) ?></h5>
                                                <small class="text-muted">GCS Files</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Upload Test -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-upload me-2"></i>
                                        Test File Upload
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <?= form_open_multipart('gcs-test/upload', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="test_file" class="form-label">Select File</label>
                                                    <input type="file" class="form-control" id="test_file" name="test_file" required>
                                                    <div class="invalid-feedback">
                                                        Please select a file to upload.
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="category" class="form-label">Category</label>
                                                    <select class="form-select" id="category" name="category" required>
                                                        <?php foreach ($gcs_config->categories as $key => $config): ?>
                                                            <option value="<?= esc($key) ?>"><?= esc(ucfirst(str_replace('_', ' ', $key))) ?></option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="sub_path" class="form-label">Sub Path (Optional)</label>
                                                    <input type="text" class="form-control" id="sub_path" name="sub_path" placeholder="e.g., user123">
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-upload me-1"></i>
                                            Upload File
                                        </button>
                                    <?= form_close() ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-tools me-2"></i>
                                        Actions
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="<?= base_url('gcs-test/files') ?>" class="btn btn-outline-primary">
                                            <i class="fas fa-list me-1"></i>
                                            View Uploaded Files
                                        </a>
                                        
                                        <?php if ($gcs_enabled && $validation['valid']): ?>
                                            <button type="button" class="btn btn-outline-warning" onclick="migrateToGcs()">
                                                <i class="fas fa-cloud-upload-alt me-1"></i>
                                                Migrate Local to GCS
                                            </button>
                                        <?php endif; ?>
                                        
                                        <button type="button" class="btn btn-outline-info" onclick="refreshStats()">
                                            <i class="fas fa-sync me-1"></i>
                                            Refresh Statistics
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test GCS connection
function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Testing...';
    btn.disabled = true;

    fetch('<?= base_url('gcs-test/test-connection') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'Connection test failed: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

// Migrate files to GCS
function migrateToGcs() {
    if (confirm('This will migrate all local files to Google Cloud Storage. Continue?')) {
        window.location.href = '<?= base_url('gcs-test/migrate') ?>';
    }
}

// Refresh statistics
function refreshStats() {
    fetch('<?= base_url('gcs-test/stats') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            showAlert('danger', 'Failed to refresh statistics');
        });
}

// Show alert
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php
// Helper function to format bytes
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>

<?= $this->endSection() ?>
