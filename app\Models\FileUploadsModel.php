<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * File Uploads Model
 * 
 * Tracks all file uploads in the system (both local and GCS)
 */
class FileUploadsModel extends Model
{
    protected $table = 'file_uploads';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'original_filename',
        'stored_filename',
        'file_path',
        'storage_type',
        'category',
        'sub_path',
        'file_size',
        'mime_type',
        'public_url',
        'uploaded_by',
        'related_table',
        'related_id',
        'metadata',
        'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $validationRules = [
        'original_filename' => 'required|max_length[255]',
        'stored_filename' => 'required|max_length[255]',
        'file_path' => 'required|max_length[500]',
        'storage_type' => 'required|in_list[local,gcs]',
        'category' => 'required|max_length[100]',
        'file_size' => 'required|integer',
        'mime_type' => 'required|max_length[100]'
    ];

    protected $validationMessages = [
        'original_filename' => [
            'required' => 'Original filename is required',
            'max_length' => 'Original filename cannot exceed 255 characters'
        ],
        'storage_type' => [
            'required' => 'Storage type is required',
            'in_list' => 'Storage type must be either local or gcs'
        ],
        'category' => [
            'required' => 'File category is required',
            'max_length' => 'Category cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    protected $allowCallbacks = true;
    protected $beforeInsert = ['beforeInsert'];
    protected $afterInsert = ['afterInsert'];
    protected $beforeUpdate = ['beforeUpdate'];
    protected $afterUpdate = ['afterUpdate'];
    protected $beforeFind = [];
    protected $afterFind = ['afterFind'];
    protected $beforeDelete = ['beforeDelete'];
    protected $afterDelete = ['afterDelete'];

    /**
     * Record file upload
     * 
     * @param array $uploadData Upload data from FileUploadHelper
     * @param string $relatedTable Related table name
     * @param int|null $relatedId Related record ID
     * @return int|false Insert ID or false on failure
     */
    public function recordUpload(array $uploadData, string $relatedTable = null, int $relatedId = null)
    {
        $data = [
            'original_filename' => $uploadData['original_name'] ?? $uploadData['filename'],
            'stored_filename' => $uploadData['filename'],
            'file_path' => $uploadData['gcs_path'] ?? $uploadData['file_path'],
            'storage_type' => $uploadData['storage_type'] ?? 'local',
            'category' => $uploadData['category'],
            'sub_path' => $uploadData['sub_path'] ?? null,
            'file_size' => $uploadData['file_size'],
            'mime_type' => $uploadData['mime_type'],
            'public_url' => $uploadData['public_url'],
            'uploaded_by' => $uploadData['uploaded_by'] ?? null,
            'related_table' => $relatedTable,
            'related_id' => $relatedId,
            'metadata' => json_encode($uploadData['metadata'] ?? []),
            'is_active' => 1
        ];

        return $this->insert($data);
    }

    /**
     * Get files by category
     * 
     * @param string $category File category
     * @param bool $activeOnly Get only active files
     * @return array
     */
    public function getFilesByCategory(string $category, bool $activeOnly = true): array
    {
        $builder = $this->where('category', $category);
        
        if ($activeOnly) {
            $builder->where('is_active', 1);
        }

        return $builder->findAll();
    }

    /**
     * Get files by related record
     * 
     * @param string $relatedTable Related table name
     * @param int $relatedId Related record ID
     * @param string|null $category Optional category filter
     * @return array
     */
    public function getFilesByRelated(string $relatedTable, int $relatedId, string $category = null): array
    {
        $builder = $this->where('related_table', $relatedTable)
                        ->where('related_id', $relatedId)
                        ->where('is_active', 1);

        if ($category) {
            $builder->where('category', $category);
        }

        return $builder->findAll();
    }

    /**
     * Get files by storage type
     * 
     * @param string $storageType Storage type (local or gcs)
     * @return array
     */
    public function getFilesByStorageType(string $storageType): array
    {
        return $this->where('storage_type', $storageType)
                    ->where('is_active', 1)
                    ->findAll();
    }

    /**
     * Get file statistics
     * 
     * @return array
     */
    public function getFileStatistics(): array
    {
        $stats = [
            'total_files' => $this->where('is_active', 1)->countAllResults(false),
            'local_files' => $this->where('storage_type', 'local')->where('is_active', 1)->countAllResults(false),
            'gcs_files' => $this->where('storage_type', 'gcs')->where('is_active', 1)->countAllResults(false),
            'total_size' => 0,
            'categories' => []
        ];

        // Get total file size
        $sizeResult = $this->selectSum('file_size', 'total_size')
                          ->where('is_active', 1)
                          ->first();
        $stats['total_size'] = $sizeResult['total_size'] ?? 0;

        // Get category breakdown
        $categoryStats = $this->select('category, COUNT(*) as count, SUM(file_size) as size')
                             ->where('is_active', 1)
                             ->groupBy('category')
                             ->findAll();

        foreach ($categoryStats as $cat) {
            $stats['categories'][$cat['category']] = [
                'count' => $cat['count'],
                'size' => $cat['size']
            ];
        }

        return $stats;
    }

    /**
     * Mark file as inactive (soft delete alternative)
     * 
     * @param int $fileId File ID
     * @return bool
     */
    public function deactivateFile(int $fileId): bool
    {
        return $this->update($fileId, ['is_active' => 0]);
    }

    /**
     * Get orphaned files (files without related records)
     * 
     * @return array
     */
    public function getOrphanedFiles(): array
    {
        return $this->where('related_table IS NOT NULL')
                    ->where('related_id IS NOT NULL')
                    ->where('is_active', 1)
                    ->findAll();
    }

    /**
     * Clean up orphaned files
     * 
     * @return int Number of files cleaned up
     */
    public function cleanupOrphanedFiles(): int
    {
        $orphanedFiles = $this->getOrphanedFiles();
        $cleanedCount = 0;

        foreach ($orphanedFiles as $file) {
            // Check if related record exists
            $relatedExists = $this->db->table($file['related_table'])
                                     ->where('id', $file['related_id'])
                                     ->countAllResults() > 0;

            if (!$relatedExists) {
                $this->deactivateFile($file['id']);
                $cleanedCount++;
            }
        }

        return $cleanedCount;
    }

    /**
     * Before insert callback
     */
    protected function beforeInsert(array $data): array
    {
        return $data;
    }

    /**
     * After insert callback
     */
    protected function afterInsert(array $data): array
    {
        if (isset($data['id'])) {
            log_message('info', "File upload recorded: ID {$data['id']}, Path: {$data['data']['file_path']}");
        }
        return $data;
    }

    /**
     * Before update callback
     */
    protected function beforeUpdate(array $data): array
    {
        return $data;
    }

    /**
     * After update callback
     */
    protected function afterUpdate(array $data): array
    {
        return $data;
    }

    /**
     * After find callback
     */
    protected function afterFind(array $data): array
    {
        // Decode JSON metadata if present
        if (isset($data['data']['metadata']) && is_string($data['data']['metadata'])) {
            $data['data']['metadata'] = json_decode($data['data']['metadata'], true);
        }

        return $data;
    }

    /**
     * Before delete callback
     */
    protected function beforeDelete(array $data): array
    {
        return $data;
    }

    /**
     * After delete callback
     */
    protected function afterDelete(array $data): array
    {
        if (isset($data['id'])) {
            log_message('info', "File upload record deleted: ID {$data['id']}");
        }
        return $data;
    }
}
