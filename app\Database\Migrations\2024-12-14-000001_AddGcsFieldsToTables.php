<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddGcsFieldsToTables extends Migration
{
    public function up()
    {
        // Add GCS fields to applicants table
        if ($this->db->tableExists('applicants')) {
            $fields = [
                'profile_photo_gcs_path' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Google Cloud Storage path for profile photo'
                ],
                'profile_photo_storage_type' => [
                    'type' => 'ENUM',
                    'constraint' => ['local', 'gcs'],
                    'default' => 'local',
                    'comment' => 'Storage type for profile photo'
                ]
            ];
            $this->forge->addColumn('applicants', $fields);
        }

        // Add GCS fields to appx_application_details table
        if ($this->db->tableExists('appx_application_details')) {
            $fields = [
                'cv_gcs_path' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Google Cloud Storage path for CV'
                ],
                'cover_letter_gcs_path' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Google Cloud Storage path for cover letter'
                ],
                'certificates_gcs_path' => [
                    'type' => 'TEXT',
                    'null' => true,
                    'comment' => 'JSON array of GCS paths for certificates'
                ],
                'documents_storage_type' => [
                    'type' => 'ENUM',
                    'constraint' => ['local', 'gcs'],
                    'default' => 'local',
                    'comment' => 'Storage type for application documents'
                ]
            ];
            $this->forge->addColumn('appx_application_details', $fields);
        }

        // Add GCS fields to dakoii_org table
        if ($this->db->tableExists('dakoii_org')) {
            $fields = [
                'signature_gcs_path' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Google Cloud Storage path for organization signature'
                ],
                'logo_gcs_path' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Google Cloud Storage path for organization logo'
                ],
                'org_storage_type' => [
                    'type' => 'ENUM',
                    'constraint' => ['local', 'gcs'],
                    'default' => 'local',
                    'comment' => 'Storage type for organization files'
                ]
            ];
            $this->forge->addColumn('dakoii_org', $fields);
        }

        // Add GCS fields to positions table
        if ($this->db->tableExists('positions')) {
            $fields = [
                'job_description_gcs_path' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Google Cloud Storage path for job description'
                ],
                'attachments_gcs_path' => [
                    'type' => 'TEXT',
                    'null' => true,
                    'comment' => 'JSON array of GCS paths for position attachments'
                ],
                'position_storage_type' => [
                    'type' => 'ENUM',
                    'constraint' => ['local', 'gcs'],
                    'default' => 'local',
                    'comment' => 'Storage type for position files'
                ]
            ];
            $this->forge->addColumn('positions', $fields);
        }

        // Create file_uploads tracking table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'original_filename' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Original filename'
            ],
            'stored_filename' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Stored filename'
            ],
            'file_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'comment' => 'File path (local or GCS)'
            ],
            'storage_type' => [
                'type' => 'ENUM',
                'constraint' => ['local', 'gcs'],
                'default' => 'local',
                'comment' => 'Storage type'
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'File category'
            ],
            'sub_path' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'Sub-path within category'
            ],
            'file_size' => [
                'type' => 'BIGINT',
                'unsigned' => true,
                'comment' => 'File size in bytes'
            ],
            'mime_type' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'MIME type'
            ],
            'public_url' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
                'comment' => 'Public URL for file access'
            ],
            'uploaded_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'User ID who uploaded the file'
            ],
            'related_table' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'comment' => 'Related table name'
            ],
            'related_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'Related record ID'
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Additional file metadata'
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'comment' => 'File active status'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['related_table', 'related_id']);
        $this->forge->addKey('uploaded_by');
        $this->forge->addKey('category');
        $this->forge->addKey('storage_type');
        $this->forge->addKey('created_at');
        
        $this->forge->createTable('file_uploads');

        // Create indexes for better performance
        $this->db->query('CREATE INDEX idx_file_uploads_path ON file_uploads(file_path)');
        $this->db->query('CREATE INDEX idx_file_uploads_category_storage ON file_uploads(category, storage_type)');
    }

    public function down()
    {
        // Remove GCS fields from applicants table
        if ($this->db->tableExists('applicants')) {
            $this->forge->dropColumn('applicants', ['profile_photo_gcs_path', 'profile_photo_storage_type']);
        }

        // Remove GCS fields from appx_application_details table
        if ($this->db->tableExists('appx_application_details')) {
            $this->forge->dropColumn('appx_application_details', [
                'cv_gcs_path', 
                'cover_letter_gcs_path', 
                'certificates_gcs_path', 
                'documents_storage_type'
            ]);
        }

        // Remove GCS fields from dakoii_org table
        if ($this->db->tableExists('dakoii_org')) {
            $this->forge->dropColumn('dakoii_org', [
                'signature_gcs_path', 
                'logo_gcs_path', 
                'org_storage_type'
            ]);
        }

        // Remove GCS fields from positions table
        if ($this->db->tableExists('positions')) {
            $this->forge->dropColumn('positions', [
                'job_description_gcs_path', 
                'attachments_gcs_path', 
                'position_storage_type'
            ]);
        }

        // Drop file_uploads table
        $this->forge->dropTable('file_uploads');
    }
}
