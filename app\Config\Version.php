<?php

namespace App\Config;

/**
 * DERS Version Information
 * 
 * This file contains version information for the DERS application.
 * Update this file when releasing new versions.
 */
class Version
{
    /**
     * Current application version
     * Format: MAJOR.MINOR.PATCH
     */
    public const VERSION = '1.1.0';

    /**
     * Version name/codename
     */
    public const VERSION_NAME = 'Notification System';

    /**
     * Release date
     */
    public const RELEASE_DATE = '2024-12-14';

    /**
     * Application name
     */
    public const APP_NAME = 'DERS';

    /**
     * Application full name
     */
    public const APP_FULL_NAME = 'Dakoii Echad Recruitment & Selection System';

    /**
     * Build information
     */
    public const BUILD_DATE = '2024-12-14';

    /**
     * Get formatted version string
     * 
     * @return string
     */
    public static function getVersion(): string
    {
        return self::VERSION;
    }

    /**
     * Get full version information
     * 
     * @return array
     */
    public static function getVersionInfo(): array
    {
        return [
            'version' => self::VERSION,
            'version_name' => self::VERSION_NAME,
            'app_name' => self::APP_NAME,
            'app_full_name' => self::APP_FULL_NAME,
            'release_date' => self::RELEASE_DATE,
            'build_date' => self::BUILD_DATE,
        ];
    }

    /**
     * Get formatted version string with app name
     * 
     * @return string
     */
    public static function getFullVersionString(): string
    {
        return self::APP_NAME . ' v' . self::VERSION . ' (' . self::VERSION_NAME . ')';
    }

    /**
     * Check if this is a development version
     * 
     * @return bool
     */
    public static function isDevelopment(): bool
    {
        return strpos(self::VERSION, 'dev') !== false || strpos(self::VERSION, 'alpha') !== false || strpos(self::VERSION, 'beta') !== false;
    }

    /**
     * Get major version number
     * 
     * @return int
     */
    public static function getMajorVersion(): int
    {
        return (int) explode('.', self::VERSION)[0];
    }

    /**
     * Get minor version number
     * 
     * @return int
     */
    public static function getMinorVersion(): int
    {
        return (int) explode('.', self::VERSION)[1];
    }

    /**
     * Get patch version number
     * 
     * @return int
     */
    public static function getPatchVersion(): int
    {
        return (int) explode('.', self::VERSION)[2];
    }
}
