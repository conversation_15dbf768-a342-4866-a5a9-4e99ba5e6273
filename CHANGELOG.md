# Changelog

All notable changes to the DERS (Dakoii Echad Recruitment & Selection System) project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- (No unreleased changes)

### Changed
- (No unreleased changes)

### Fixed
- (No unreleased changes)

## [1.1.0] - 2024-12-14

### Added
- Application notifications system with comprehensive tracking
- Notification status dashboard with pending alerts
- User accountability for notification sending
- Professional email templates with DERS branding
- Notification statistics across all organizational levels
- RESTful navigation structure for notifications
- Email service methods for successful/unsuccessful notifications
- CSRF protection for notification forms

### Changed
- Enhanced dashboard with notification status indicators
- Improved email template consistency and professional appearance
- Updated application reports formatting
- Email sign-off format to "Best Regards, The DERS (Organization)"

### Fixed
- CSRF token validation for notification forms
- Email template styling and emoji removal
- Application count field naming consistency
- SecurityException for notification form submissions

## [1.0.0] - 2024-12-14

### Added
- **Core System Features**
  - User authentication and role-based access control
  - Organization management with multi-tenant support
  - Exercise and position group management
  - Position creation and management with detailed specifications
  - Application submission and tracking system
  - Applicant profile management with document uploads

- **Application Processing**
  - Application acknowledgment system with email notifications
  - Profiling and pre-screening functionality
  - Shortlisting and elimination processes
  - Application status tracking and management
  - Document verification and management

- **Reporting and Analytics**
  - Comprehensive dashboard with real-time statistics
  - Application register reports with filtering
  - Position-wise application tracking
  - Statistical summaries and analytics
  - PDF export functionality for reports

- **Email System**
  - SMTP email configuration and service
  - Professional email templates for various stages
  - Acknowledgment email notifications
  - Profiling and pre-screening notifications
  - Shortlisting result notifications

- **User Interface**
  - Responsive Bootstrap 5 design
  - Professional admin dashboard
  - Intuitive navigation and user experience
  - Form validation and error handling
  - File upload and management interface

- **Security Features**
  - CSRF protection for all forms
  - Input validation and sanitization
  - Secure file upload handling
  - Session management and security
  - Role-based access control

### Technical Implementation
- **Framework**: CodeIgniter 4
- **Database**: MySQL with proper relationships
- **Frontend**: Bootstrap 5, Font Awesome, jQuery
- **Email**: SMTP integration with professional templates
- **File Handling**: Secure upload and storage system
- **Architecture**: MVC pattern with service layer

### Database Schema
- Users and authentication tables
- Organization and exercise management
- Position and application tracking
- Document and file management
- Audit trails and logging

---

## Version Numbering

This project follows [Semantic Versioning](https://semver.org/):
- **MAJOR** version for incompatible API changes and major system upgrades
- **MINOR** version for new functionality in a backwards compatible manner
- **PATCH** version for backwards compatible bug fixes and minor improvements

## Release Process

1. Update version numbers in relevant files
2. Update this CHANGELOG.md with new changes
3. Create git tag with version number
4. Create GitHub release with release notes
5. Deploy to production environment

## Support

For questions about specific versions or upgrade paths, please refer to:
- [VERSIONING.md](VERSIONING.md) for detailed versioning guidelines
- [README.md](README.md) for installation and setup instructions
- GitHub Issues for bug reports and feature requests
