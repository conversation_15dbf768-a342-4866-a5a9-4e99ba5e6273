<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Dakoii Admin" content="This is Dakoii Admin Interface" />
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">

    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Font Awesome - Local Installation -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/fontawesome/fontawesome-free-6.4.0-web/css/all.min.css">
    <!-- Google Fonts - Roboto -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />

    <!-- Custom CSS for dark theme -->
    <style>
        :root {
            /* Primary and accent colors with good contrast */
            --primary-color: #5e97f6;      /* Brightened blue */
            --primary-hover: #7ba9f7;      /* Lighter blue for hover states */
            --secondary-color: #4bd0a0;    /* Brightened green */
            --secondary-hover: #67e5b5;    /* Lighter green for hover */
            --accent-color: #ffcc80;       /* Soft orange accent */

            /* Background colors - shaded dark theme */
            --dark-bg: #1e2235;           /* Main background */
            --darker-bg: #171a29;         /* Darker areas like navbar */
            --light-bg: #2a2f45;          /* Card backgrounds */
            --lighter-bg: #353b50;        /* Highlighted areas */

            /* Text colors with proper contrast */
            --light-text: #ffffff;         /* Primary text - pure white for maximum contrast */
            --secondary-text: #e0e0e0;     /* Secondary text - slightly dimmed */
            --muted-text: #b4bac6;        /* Muted text for less emphasis */
            --link-text: #82b1ff;         /* Links - bright blue */

            /* Component colors */
            --card-bg: #2a2f45;           /* Card backgrounds */
            --border-color: #3c4358;      /* Borders */
            --input-bg: rgba(255, 255, 255, 0.08); /* Slightly lighter input fields */
            --input-border: #4c5366;      /* More visible input borders */
            --hover-bg: rgba(94, 151, 246, 0.15); /* Hover background */

            /* Alert and status colors */
            --success-color: #4cd964;     /* Brighter success green */
            --warning-color: #ffcc00;     /* More visible warning yellow */
            --danger-color: #ff3b30;      /* Brighter danger red */
            --info-color: #5ac8fa;        /* Brighter info blue */
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--dark-bg);
            color: var(--light-text);
            line-height: 1.6;
        }

        .navbar {
            background-color: var(--darker-bg) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--light-text) !important;
        }

        .nav-link {
            color: var(--secondary-text) !important;
            font-weight: 500;
            padding: 0.7rem 1rem !important;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--light-text) !important;
            background-color: var(--hover-bg);
        }

        .nav-link.active {
            border-left: 3px solid var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--light-text);
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--light-text);
        }

        .btn-success:hover {
            background-color: var(--secondary-hover);
            border-color: var(--secondary-hover);
        }

        .btn-info {
            background-color: var(--info-color);
            border-color: var(--info-color);
            color: var(--light-text) !important;
        }

        .btn-info:hover {
            background-color: #72d4ff;
            border-color: #72d4ff;
            color: var(--light-text) !important;
        }

        .btn-outline-secondary {
            color: var(--secondary-text);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--border-color);
            color: var(--light-text);
            border-color: var(--border-color);
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            margin-bottom: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            background-color: rgba(0, 0, 0, 0.15);
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
            padding: 1rem 1.25rem;
            color: var(--light-text);
        }

        /* Card header variants */
        .card-header.bg-lighter-bg {
            background-color: var(--lighter-bg);
            color: var(--light-text);
            border-bottom: 1px solid var(--border-color);
        }

        .card-header .text-light-text,
        .card-header h1,
        .card-header h2,
        .card-header h3,
        .card-header h4,
        .card-header h5,
        .card-header h6 {
            color: var(--light-text);
        }

        .card-body {
            padding: 1.25rem;
        }

        .table {
            color: var(--light-text);
            border-color: var(--border-color);
        }

        .table thead th {
            border-bottom: 2px solid var(--border-color);
            background-color: rgba(0, 0, 0, 0.15);
            color: var(--secondary-text);
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.5px;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.075);
        }

        /* Form controls with better contrast */
        .form-control, .form-select, .input-group-text {
            background-color: var(--input-bg);
            color: var(--light-text);
            border-color: var(--input-border);
        }

        .form-control::placeholder {
            color: var(--muted-text);
            opacity: 0.7;
        }

        .form-control:focus, .form-select:focus {
            background-color: rgba(255, 255, 255, 0.12);
            color: var(--light-text);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(94, 151, 246, 0.25);
        }

        .form-label {
            color: var(--secondary-text);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-text {
            color: var(--muted-text);
        }

        /* Status color classes */
        .bg-primary, .badge-primary, .btn-primary {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        .bg-success, .badge-success, .btn-success, .alert-success {
            background-color: var(--success-color) !important;
            border-color: var(--success-color) !important;
        }

        .bg-danger, .badge-danger, .btn-danger, .alert-danger {
            background-color: var(--danger-color) !important;
            border-color: var(--danger-color) !important;
        }

        .bg-warning, .badge-warning, .btn-warning, .alert-warning {
            background-color: var(--warning-color) !important;
            border-color: var(--warning-color) !important;
            color: #212529 !important;
        }

        .bg-info, .badge-info, .btn-info, .alert-info {
            background-color: var(--info-color) !important;
            border-color: var(--info-color) !important;
        }

        /* Alert styling */
        .alert {
            border-radius: 8px;
            border: none;
            font-weight: 500;
        }

        .alert-success {
            background-color: rgba(76, 217, 100, 0.15) !important;
            color: #65e87b !important;
            border-left: 4px solid var(--success-color) !important;
        }

        .alert-danger {
            background-color: rgba(255, 59, 48, 0.15) !important;
            color: #ff6b62 !important;
            border-left: 4px solid var(--danger-color) !important;
        }

        /* Text utilities */
        .text-primary {
            color: var(--primary-color) !important;
        }

        .text-success {
            color: var(--success-color) !important;
        }

        .text-danger {
            color: var(--danger-color) !important;
        }

        .text-warning {
            color: var(--warning-color) !important;
        }

        .text-info {
            color: var(--info-color) !important;
        }

        .text-muted {
            color: var(--muted-text) !important;
        }

        a {
            color: var(--link-text);
            text-decoration: none;
            transition: color 0.2s;
        }

        a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        /* Breadcrumb styling */
        .breadcrumb {
            border: 1px solid var(--border-color);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .breadcrumb-item {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: var(--secondary-text);
            content: "\f054";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            font-size: 0.7rem;
            padding: 0 0.75rem;
        }

        .breadcrumb-item a {
            color: var(--light-text);
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: var(--accent-color);
            font-weight: 600;
        }

        /* Continue with existing styles... */
        .navbar {
            background-color: var(--darker-bg) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            color: var(--muted-text) !important;
            font-weight: 500;
            padding: 0.7rem 1rem !important;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--light-text) !important;
            background-color: var(--hover-bg);
        }

        .nav-link.active {
            border-left: 3px solid var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #3a79cc;
            border-color: #3a79cc;
        }

        .btn-success {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-success:hover {
            background-color: #2dae8b;
            border-color: #2dae8b;
        }

        .btn-info {
            background-color: var(--info-color);
            border-color: var(--info-color);
        }

        .btn-info:hover {
            background-color: #3eb1d9;
            border-color: #3eb1d9;
        }

        .btn-outline-secondary {
            color: var(--muted-text);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--border-color);
            color: var(--light-text);
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            margin-bottom: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            background-color: rgba(0, 0, 0, 0.15);
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
            padding: 1rem 1.25rem;
        }

        .card-body {
            padding: 1.25rem;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .bg-success {
            background-color: var(--success-color) !important;
        }

        .bg-danger {
            background-color: var(--danger-color) !important;
        }

        .bg-warning {
            background-color: var(--warning-color) !important;
        }

        .bg-info {
            background-color: var(--info-color) !important;
        }

        .badge {
            padding: 0.5em 0.75em;
            font-weight: 500;
            border-radius: 4px;
        }

        .badge-success {
            background-color: var(--success-color);
        }

        .badge-danger {
            background-color: var(--danger-color);
        }

        .badge-warning {
            background-color: var(--warning-color);
        }

        .badge-info {
            background-color: var(--info-color);
        }

        .table {
            color: var(--light-text);
            border-color: var(--border-color);
        }

        .table thead th {
            border-bottom: 2px solid var(--border-color);
            background-color: rgba(0, 0, 0, 0.15);
            color: var(--muted-text);
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.5px;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.075);
        }

        .toast-success {
            background-color: var(--success-color);
        }

        .toast-error {
            background-color: var(--danger-color);
        }

        .footer {
            margin-top: 40px;
            padding: 20px 0;
            background-color: var(--darker-bg) !important;
            border-top: 1px solid var(--border-color);
        }

        .sidebar {
            min-height: 100vh;
            background-color: var(--darker-bg);
            box-shadow: 2px 0 10px rgba(0,0,0,0.2);
            padding-top: 1rem;
            position: sticky;
            top: 0;
            border-right: 1px solid var(--border-color);
        }

        .content-wrapper {
            padding: 20px;
        }

        /* Dashboard stats */
        .stat-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 1rem;
            color: var(--muted-text);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-color);
            opacity: 0.8;
        }

        /* Alert customization */
        .alert {
            border-radius: 8px;
            border: none;
        }

        .alert-success {
            background-color: rgba(55, 188, 155, 0.15);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.15);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        /* Form styling */
        .form-control, .form-select {
            background-color: var(--light-bg);
            border-color: var(--border-color);
            color: var(--light-text);
            border-radius: 6px;
            padding: 0.6rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--lighter-bg);
            color: var(--light-text);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(74, 137, 220, 0.25);
        }

        .form-label {
            color: var(--muted-text);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-text {
            color: var(--muted-text);
        }

        /* Modal styling */
        .modal-content {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
        }

        .modal-header, .modal-footer {
            border-color: var(--border-color);
        }

        .modal-header .close {
            color: var(--light-text);
        }

        /* Dropdown styling */
        .dropdown-menu {
            background-color: var(--light-bg);
            border-color: var(--border-color);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            padding: 0.5rem;
        }

        .dropdown-item {
            color: var(--light-text);
            padding: 0.6rem 1rem;
            border-radius: 4px;
        }

        .dropdown-item:hover {
            background-color: var(--hover-bg);
            color: var(--light-text);
        }

        .dropdown-divider {
            border-color: var(--border-color);
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--darker-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* Custom card-stat class for dashboard */
        .card-stat {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
        }

        .card-stat:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
        }
    </style>

    <title><?= $title ?> | Dakoii Admin</title>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <script>
        $(function() {
            // For Bootstrap 5, update the file input display with the selected filename
            $(document).on('change', '.form-control[type=file]', function() {
                // Get the file name
                var fileName = $(this).val().split('\\').pop();
                // Display the file name
                if (fileName) {
                    $(this).next('.form-text').text(fileName);
                }
            });

            // Configure Toastr
            toastr.options = {
                closeButton: true,
                newestOnTop: true,
                progressBar: true,
                positionClass: "toast-top-right",
                preventDuplicates: false,
                showDuration: "300",
                hideDuration: "1000",
                timeOut: "5000",
                extendedTimeOut: "1000",
                showEasing: "swing",
                hideEasing: "linear",
                showMethod: "fadeIn",
                hideMethod: "fadeOut"
            };

            <?php if (session()->getFlashdata('error')) : ?>
                toastr.error("<?= session()->getFlashdata('error') ?>");
            <?php endif ?>
            <?php if (session()->getFlashdata('success')) : ?>
                toastr.success("<?= session()->getFlashdata('success') ?>");
            <?php endif ?>
        });

        // Update Bootstrap 4 data attributes to Bootstrap 5 format
        $(document).ready(function() {
            // Find all elements with data-toggle and update to data-bs-toggle
            $('[data-toggle]').each(function() {
                var attr = $(this).attr('data-toggle');
                $(this).attr('data-bs-toggle', attr);
                $(this).removeAttr('data-toggle');
            });

            // Find all elements with data-target and update to data-bs-target
            $('[data-target]').each(function() {
                var attr = $(this).attr('data-target');
                $(this).attr('data-bs-target', attr);
                $(this).removeAttr('data-target');
            });

            // Find all elements with data-dismiss and update to data-bs-dismiss
            $('[data-dismiss]').each(function() {
                var attr = $(this).attr('data-dismiss');
                $(this).attr('data-bs-dismiss', attr);
                $(this).removeAttr('data-dismiss');
            });
        });
    </script>
</head>

<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center py-4">
                    <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii Logo" width="60" height="60" class="mb-3">
                    <h5 class="fw-bold"><?= SYSTEM_NAME ?></h5>
                    <p class="text-muted small">Admin Dashboard</p>
                </div>

                <hr class="my-3">

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'dashboard') ? 'active' : '' ?>" href="<?= base_url('dakoii/dashboard') ?>">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'organizations') ? 'active' : '' ?>" href="<?= base_url('dakoii/organization/list') ?>">
                            <i class="fas fa-building"></i> Organizations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'provinces') ? 'active' : '' ?>" href="<?= base_url('dakoii/province/list') ?>">
                            <i class="fas fa-map-marked-alt"></i> Provinces
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'rating_items') ? 'active' : '' ?>" href="<?= base_url('dakoii/rating_items/list') ?>">
                            <i class="fas fa-star"></i> Rating Items
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'gcs-test') ? 'active' : '' ?>" href="<?= base_url('gcs-test') ?>">
                            <i class="fas fa-cloud"></i> GCS Test
                        </a>
                    </li>
                    <li class="nav-item mt-4">
                        <a class="nav-link text-danger" href="<?= base_url('dakoii/logout') ?>">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>

                <hr class="my-3">

                <div class="px-3 mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user-circle text-primary me-2 fs-4"></i>
                        <div>
                            <span class="small text-muted">Logged in as</span>
                            <div class="fw-bold"><?= session('name') ?? 'Admin User' ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Top navbar -->
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler d-md-none me-2" type="button" data-bs-toggle="collapse" data-bs-target=".sidebar" aria-controls="sidebar" aria-expanded="false" aria-label="Toggle navigation">
                            <span class="navbar-toggler-icon"></span>
                        </button>

                        <a class="navbar-brand d-md-none" href="<?= base_url() ?>">
                            <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Brand Logo" width="30" height="30" class="d-inline-block align-text-top me-2">
                            <?= SYSTEM_NAME ?>
                        </a>

                        <div class="collapse navbar-collapse justify-content-end">
                            <ul class="navbar-nav">
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-user-circle"></i> <?= session('username') ?? 'Admin' ?>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end dropdown-menu-dark" aria-labelledby="navbarDropdown">
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i> Profile</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="<?= base_url('dakoii/logout') ?>"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </nav>

                <!-- Page heading -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                    <h1 class="h2"><?= $title ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-decoration-none">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?= $title ?></li>
                        </ol>
                    </nav>
                </div>

                <!-- Alert messages -->
                <?php if ($success = session()->getFlashdata('success')) : ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i> <?= $success ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error = session()->getFlashdata('error')) : ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i> <?= $error ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Main content section -->
                <div class="content">
                    <?= $this->renderSection('content') ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <p class="text-muted mb-0">&copy; <?= date('Y') ?> <a href="https://www.dakoiims.com" class="text-decoration-none">Dakoii Systems</a></p>
                        <p class="text-muted mb-0"><?= SYSTEM_NAME ?> <span class="badge bg-secondary"><?= SYSTEM_VERSION ?></span></p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

</body>
</html>